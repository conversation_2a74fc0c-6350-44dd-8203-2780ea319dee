/* Dashboard Spacing Override - Loads last to ensure proper spacing in both dev and preview */

/* Ultra-high specificity overrides for dashboard history spacing */
html body #root .dashboard-history,
html body #root div.dashboard-history,
html body #root .dashboard-history.p-4,
html body #root .dashboard-history.p-6 {
    padding: 1rem !important;
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
    padding-left: 1rem !important;
    padding-right: 1rem !important;
}

/* Header spacing override */
html body #root .dashboard-history > div:first-child,
html body #root .dashboard-history > div.flex.items-center.justify-between,
html body #root .dashboard-history > div.flex.items-center.justify-between.mb-4,
html body #root .dashboard-history > div.flex.items-center.justify-between.mb-6 {
    margin-bottom: 1rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
}

/* History grid spacing override */
html body #root .dashboard-history .history-grid,
html body #root .history-grid {
    gap: 0.5rem !important;
    display: grid !important;
    grid-template-columns: 1fr !important;
    overflow-y: auto !important;
    overflow-x: visible !important;
}

/* Responsive grid overrides */
@media (min-width: 640px) {
    html body #root .dashboard-history .history-grid,
    html body #root .history-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

@media (min-width: 900px) {
    html body #root .dashboard-history .history-grid,
    html body #root .history-grid {
        grid-template-columns: repeat(3, 1fr) !important;
    }
}

/* Ensure no extra margins or padding on grid items */
html body #root .dashboard-history .history-grid .history-item {
    margin: 0 !important;
    padding: 0 !important;
}
