import React from 'react'
import ReactDOM from 'react-dom/client'
import { Entry } from './Entry.jsx'
import { ErrorBoundary } from './components/ui/ErrorBoundary.jsx'
import { enableZoomLock } from './utils/mobileZoomManager.js';

// Call zoom lock immediately
enableZoomLock();

// Import all CSS files
import './styles/geist-fonts.css'
import './styles/layout.css'
import './styles/controls.css'
import './styles/preview.css'
import './styles/prompt.css'
import './styles/dashboard.css'
import './styles/user-profile-section.css'
import './styles/collapsible-sections.css'
import './styles/confirmation-modal.css'
import './styles/generation-details-modal.css'
import './styles/global-tooltips.css'
import './styles/admin.css'
import './styles/dashboard-premium-transitions.css'
import './styles/app-modal-styles.css'
import './styles/face-upload-section.css'
import './styles/toast.css'
import './styles/auth-buttons.css'
import './styles/registration-success.css'
import './styles/auth-glass-v2.css'
import './styles/image-requirements-carousel.css'
import './styles/dashboard-spacing-override.css'

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <ErrorBoundary>
      <Entry />
    </ErrorBoundary>
  </React.StrictMode>,
) 